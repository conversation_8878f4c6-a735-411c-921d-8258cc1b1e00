# Generated by Django 4.0.2 on 2022-02-23 19:06

from django import VERSION
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testapp', '0016_jsonmodel'),
    ]

    operations = [
        migrations.CreateModel(
            name='BinaryData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('binary', models.BinaryField(max_length='max', null=True)),
            ],
        ),
    ]

    if VERSION >= (3, 2):
        operations += [
            migrations.CreateModel(
                name='TestCheckConstraintWithUnicode',
                fields=[
                    ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                    ('name', models.CharField(max_length=100)),
                ],
                options={
                    'required_db_features': {'supports_table_check_constraints'},
                },
            ),
            migrations.AddConstraint(
                model_name='testcheckconstraintwithunicode',
                constraint=models.CheckConstraint(check=models.Q(('name__startswith', '÷'), _negated=True), name='name_does_not_starts_with_÷'),
            ),
        ]
