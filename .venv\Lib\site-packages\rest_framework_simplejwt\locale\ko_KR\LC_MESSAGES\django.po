# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: 정재균 <<EMAIL>>\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "인증 헤더에는 공백으로 구분 된 두 개의 값이 포함되어야 합니다"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "이 토큰은 모든 타입의 토큰에 대해 유효하지 않습니다"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "토큰에 사용자 식별자가 포함되어 있지 않습니다"

#: authentication.py:135
msgid "User not found"
msgstr "찾을 수 없는 사용자입니다"

#: authentication.py:139
msgid "User is inactive"
msgstr "비활성화된 사용자입니다"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr "사용자의 비밀번호가 바뀌었습니다."

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "'{}' 는 알 수 없는 알고리즘 유형입니다"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "{}를 사용하려면 암호화가 설치되어 있어야 합니다."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"알 수 없는 타입 '{}', 'leeway' 값은 반드시 int, float 또는 timedelta 타입이어"
"야 합니다."

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "유효하지 않거나 만료된 토큰입니다"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "잘못된 알고리즘이 지정되었습니다"

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "유효하지 않거나 만료된 토큰입니다"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "유효하지 않거나 만료된 토큰입니다"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "지정된 자격 증명에 해당하는 활성화된 사용자를 찾을 수 없습니다"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "지정된 자격 증명에 해당하는 활성화된 사용자를 찾을 수 없습니다"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "블랙리스트에 추가된 토큰입니다"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "'{}' 설정이 제거되었습니다. 사용 가능한 설정은 '{}'을 참조하십시오."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "사용자"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "생성 시간"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "만료 시간"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "토큰 블랙리스트"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "타입 또는 수명이 없는 토큰을 생성할 수 없습니다"

#: tokens.py:127
msgid "Token has no id"
msgstr "토큰에 식별자가 주어지지 않았습니다"

#: tokens.py:139
msgid "Token has no type"
msgstr "토큰 타입이 주어지지 않았습니다"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "잘못된 토큰 타입입니다"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "토큰에 '{}' 클레임이 없습니다"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "토큰 '{}' 클레임이 만료되었습니다"
