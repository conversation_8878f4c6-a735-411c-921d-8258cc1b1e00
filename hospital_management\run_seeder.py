#!/usr/bin/env python
"""
Simple script to run the seeder through Django shell
"""
import os
import sys

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospital_management.settings')

# Setup Django
import django
django.setup()

# Now import and run the seeder
print("Starting database seeding...")

try:
    from seed_database import main
    main()
    print("✓ Seeding completed successfully!")
except Exception as e:
    print(f"✗ Error during seeding: {e}")
    import traceback
    traceback.print_exc()
