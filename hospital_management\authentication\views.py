from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.views import APIView
from django.contrib.auth import update_session_auth_hash
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
from drf_spectacular.openapi import OpenApiTypes
from .serializers import (
    CustomTokenObtainPairSerializer,
    NguoiDungRegistrationSerializer,
    NguoiDungSerializer,
    ChangePasswordSerializer
)
from .models import NguoiDung


@extend_schema(
    operation_id='auth_login',
    tags=['Authentication'],
    summary='User login',
    description='Authenticate user with phone number and password, returns JWT tokens',
)
class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


@extend_schema_view(
    post=extend_schema(
        operation_id='auth_register',
        tags=['Authentication'],
        summary='User registration',
        description='Register a new user account',
        request=NguoiDungRegistrationSerializer,
        responses={201: NguoiDungSerializer}
    )
)
class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = NguoiDungRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'message': 'Đăng ký thành công',
                'user': NguoiDungSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema_view(
    get=extend_schema(
        operation_id='auth_profile_get',
        tags=['Authentication'],
        summary='Get user profile',
        description='Get current user profile information',
        responses={200: NguoiDungSerializer}
    ),
    patch=extend_schema(
        operation_id='auth_profile_update',
        tags=['Authentication'],
        summary='Update user profile',
        description='Update current user profile information',
        request=NguoiDungSerializer,
        responses={200: NguoiDungSerializer}
    )
)
class ProfileView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        serializer = NguoiDungSerializer(request.user)
        return Response(serializer.data)
    
    def patch(self, request):
        serializer = NguoiDungSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema_view(
    post=extend_schema(
        operation_id='auth_change_password',
        tags=['Authentication'],
        summary='Change password',
        description='Change user password',
        request=ChangePasswordSerializer,
        responses={200: {'type': 'object', 'properties': {'message': {'type': 'string'}}}}
    )
)
class ChangePasswordView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            
            # Kiểm tra mật khẩu cũ
            if not user.check_password(serializer.validated_data['mat_khau_cu']):
                return Response({
                    'mat_khau_cu': ['Mật khẩu cũ không đúng.']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Đổi mật khẩu
            user.set_password(serializer.validated_data['mat_khau_moi'])
            user.save()
            
            # Cập nhật session
            update_session_auth_hash(request, user)
            
            return Response({
                'message': 'Đổi mật khẩu thành công'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    operation_id='auth_check_permissions',
    tags=['Authentication'],
    summary='Check user permissions',
    description='Get current user role and permissions',
    responses={200: {
        'type': 'object',
        'properties': {
            'vai_tro': {'type': 'string'},
            'is_admin': {'type': 'boolean'},
            'is_doctor': {'type': 'boolean'},
            'is_patient': {'type': 'boolean'},
            'is_staff': {'type': 'boolean'},
            'trang_thai': {'type': 'string'}
        }
    }}
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_permission(request):
    """Kiểm tra quyền của user hiện tại"""
    user = request.user
    permissions_data = {
        'vai_tro': user.vai_tro,
        'is_admin': user.vai_tro == 'Quan tri vien',
        'is_doctor': user.vai_tro == 'Bac si',
        'is_patient': user.vai_tro == 'Benh nhan',
        'is_staff': user.vai_tro == 'Nhan vien y te',
        'trang_thai': user.trang_thai
    }
    return Response(permissions_data)
