# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: zeack <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"El encabezado 'Authorization' debe contener valores delimitados por espacios"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "El token dado no es valido para ningun tipo de token"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "El token no contenía identificación de usuario reconocible"

#: authentication.py:135
msgid "User not found"
msgstr "Usuario no encontrado"

#: authentication.py:139
msgid "User is inactive"
msgstr "El usuario está inactivo"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Tipo de algoritmo no reconocido '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Debe tener criptografía instalada para usar {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "El token es inválido o ha expirado"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Algoritmo especificado no válido"

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "El token es inválido o ha expirado"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "El token es inválido o ha expirado"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "La combinación de credenciales no tiene una cuenta activa"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "La combinación de credenciales no tiene una cuenta activa"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "El token está en lista negra"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"La configuración '{}' fue removida. Por favor, refiérase a '{}' para "
"consultar las disponibles."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "usuario"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "creado en"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "expira en"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Lista negra de Tokens"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "No se puede crear un token sin tipo o de tan larga vida"

#: tokens.py:127
msgid "Token has no id"
msgstr "El token no tiene id"

#: tokens.py:139
msgid "Token has no type"
msgstr "El token no tiene tipo"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "El token tiene un tipo incorrecto"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "El token no tiene el privilegio '{}'"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "El privilegio '{}' del token ha expirado"
