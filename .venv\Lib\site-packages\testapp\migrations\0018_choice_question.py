# Generated by Django 3.2.12 on 2022-03-14 18:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('testapp', '0017_binarydata_testcheckconstraintwithunicode_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.CharField(max_length=200)),
                ('pub_date', models.DateTimeField(verbose_name='date published')),
            ],
        ),
        migrations.CreateModel(
            name='Choice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('choice_text', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('votes', models.IntegerField(default=0)),
                ('question', models.<PERSON><PERSON><PERSON>(null=True, on_delete=django.db.models.deletion.CASCADE, to='testapp.question')),
            ],
            options={
                'unique_together': {('question', 'choice_text')},
            },
        ),
    ]
