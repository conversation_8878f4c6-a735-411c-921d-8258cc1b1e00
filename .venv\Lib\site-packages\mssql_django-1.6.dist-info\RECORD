mssql/__init__.py,sha256=ukmdB-0csV_kKJq5dGVgCHVDLJ8ijh8sYgHxqqISYpI,105
mssql/__pycache__/__init__.cpython-313.pyc,,
mssql/__pycache__/base.cpython-313.pyc,,
mssql/__pycache__/client.cpython-313.pyc,,
mssql/__pycache__/compiler.cpython-313.pyc,,
mssql/__pycache__/creation.cpython-313.pyc,,
mssql/__pycache__/features.cpython-313.pyc,,
mssql/__pycache__/functions.cpython-313.pyc,,
mssql/__pycache__/introspection.cpython-313.pyc,,
mssql/__pycache__/operations.cpython-313.pyc,,
mssql/__pycache__/schema.cpython-313.pyc,,
mssql/base.py,sha256=qlWu_fajWXwZIgBJTGJ4x-WlP-D7z_OqP0mt6d5I6Lw,28006
mssql/client.py,sha256=MGq87gHm9HY-L3bZJjCbtXRXhmIZM5Ya-2fyeGqki6U,1926
mssql/compiler.py,sha256=kDfsIT7J7cWqcQUsN_y5R4EsJQtIg3lrGEEzIPd2iRg,30759
mssql/creation.py,sha256=uuk4hat5xB4h3_MFoMi0z076PZEL1jjOtlNuSwoP7w4,6030
mssql/features.py,sha256=03L6AEOWYW_mAQ7HSB9LehEMYxjY6uFB_-KzTPJxeBc,3043
mssql/functions.py,sha256=wyIWb_oynWTRxCtObcYuIGydc3bvJknTK1GlfcUIn6Q,27788
mssql/introspection.py,sha256=B0iRqsEYE1tP0L5TueO33EAvhQuZy9RLPdnQftbJoxs,20322
mssql/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mssql/management/__pycache__/__init__.cpython-313.pyc,,
mssql/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mssql/management/commands/__pycache__/__init__.cpython-313.pyc,,
mssql/management/commands/__pycache__/inspectdb.cpython-313.pyc,,
mssql/management/commands/__pycache__/install_regex_clr.cpython-313.pyc,,
mssql/management/commands/inspectdb.py,sha256=7CSpk3nQQPhv-P5qT3SHQtZnUozA8nPF3LC0cwkZt-M,656
mssql/management/commands/install_regex_clr.py,sha256=ad6ZMuUJ4hGod7zpE2Some8z4bcqu3LvksKJfbGvvdY,850
mssql/operations.py,sha256=vq0DONkxMr1f-YmDfmu1nadFvguW1YORKKbopQzXEzQ,29125
mssql/regex_clr.dll,sha256=iV_GSsEwfLQm0OydgWhgx93_ed5rblMRkY1Dm_rmLVc,4608
mssql/schema.py,sha256=nnwgzMsDyefiX1vZHLTTIrVRlivIHX_57I50Oru7X0U,78957
mssql_django-1.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mssql_django-1.6.dist-info/METADATA,sha256=Yt4BqI4SrvEWNJEzGoLOhaFRXBhfnzvgOGee5s3CJr0,15229
mssql_django-1.6.dist-info/RECORD,,
mssql_django-1.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mssql_django-1.6.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
mssql_django-1.6.dist-info/licenses/LICENSE.txt,sha256=5o4efr9mnQdXF-kfOCCDr9fBT69qtsGtv5dBfrGwMRI,1673
mssql_django-1.6.dist-info/licenses/NOTICE.md,sha256=piQyMHsK8VdNrmHNU6e3XoGyqcrE7_KmUcsctu3UNUQ,1791
mssql_django-1.6.dist-info/top_level.txt,sha256=IMSjWZ-ODKHfksFunUt2sq-3N_ut5pm4IdjU2Fy8bYA,14
testapp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
testapp/__pycache__/__init__.cpython-313.pyc,,
testapp/__pycache__/models.cpython-313.pyc,,
testapp/__pycache__/runners.cpython-313.pyc,,
testapp/__pycache__/settings.cpython-313.pyc,,
testapp/migrations/0001_initial.py,sha256=BffMMdQMDp8YeqSTDtaTIi-obhErqXAwdp3Y2_Frfao,2392
testapp/migrations/0002_test_unique_nullable_part1.py,sha256=IcLkTXyY4WFhxm2ymv1B9XqOUvzlDyzItQzagVDx4kM,772
testapp/migrations/0003_test_unique_nullable_part2.py,sha256=vdvBEsXECxUDY1nQoFNWYclAGlx63IgXMsGrJl9lpzg,810
testapp/migrations/0004_test_unique_type_change_part1.py,sha256=pbgE-HVV3vTeHHLUK2jF-mdhQ2KDHOAtZfxoE3KzYDI,1023
testapp/migrations/0005_test_unique_type_change_part2.py,sha256=Y2EOPrsBEUM-cPLaaCaulyP9Vu1BaKlqtCNuq6YX5_E,1407
testapp/migrations/0006_test_remove_onetoone_field_part1.py,sha256=EQkvyAKi0T8Un9TN3b5eu8pbQAasb4v-aXyxOiaoebI,743
testapp/migrations/0007_test_remove_onetoone_field_part2.py,sha256=I9mwGlMpyOUrdaZxswkPpx-MJ_YYzX45myWa133Hhf4,376
testapp/migrations/0008_test_drop_table_with_foreign_key_reference_part1.py,sha256=7iU6CWNLUNrd3e3e9O2W1OIoP8vZtksROlEVjvsusGY,584
testapp/migrations/0009_test_drop_table_with_foreign_key_reference_part2.py,sha256=6ZqtegdWyUNrRwgmRUnvSHWhPcgSxQwfukk4-HLC6gc,477
testapp/migrations/0010_pizza_topping.py,sha256=8ZgmWIQBs92UtCBQZdY48Bch0ETPMR6Z_HOXb4_QHW4,761
testapp/migrations/0011_test_unique_constraints.py,sha256=Do3idFoeHofx2_llZ_pWnlcGBNjjFmpHNDmTNqj-iGU,2380
testapp/migrations/0012_test_indexes_retained_part1.py,sha256=xrkeqzgHDvQlk6mgCj2b_ChGHKQJQ11NE38zdlI1QVY,678
testapp/migrations/0013_test_indexes_retained_part2.py,sha256=Dt8rIh-7A-SJfizb_kbF1bcpaII8ATgAv6geFfXgTbY,797
testapp/migrations/0014_test_rename_m2mfield_part1.py,sha256=MMyr5ChG3CfJOV5iCVhnpONhcJ_SDcqcByzd2vvVhT4,887
testapp/migrations/0015_test_rename_m2mfield_part2.py,sha256=YtCkjR5RGyTPP3h2vC_hU7YpxY2URrFob1bdDKIq9dU,597
testapp/migrations/0016_jsonmodel.py,sha256=sAv8bDzWjG5favop90hWXNC2p-zBJC4n5OcqBw8a2Jo,774
testapp/migrations/0017_binarydata_testcheckconstraintwithunicode_and_more.py,sha256=xZF4OWdaj0sWlPIO_LIXwgfkzK1a_quuuMZa-aNc-10,1335
testapp/migrations/0018_choice_question.py,sha256=3KbYlMtssV2xAtRDSTWNqpPvQAd7BxpSvD5KcMftFSg,1252
testapp/migrations/0019_customer_name_customer_address.py,sha256=GMHzbozaUSsO5PiCA5k8ZukivhWKz_ESXYn3Sm1PQGo,1161
testapp/migrations/0020_autofield_to_bigautofield.py,sha256=V7O2WCq4sS_4Rs9ioEsTyCpHBrYvShBTaJw7HJvOlpQ,418
testapp/migrations/0021_multiple_autofield_to_bigauto.py,sha256=_R7Ru6plBtTXjTYXB6tSNI5BcjfGqr7ByqyL9a35qhA,790
testapp/migrations/0022_timezone.py,sha256=wMMeFuNIsXDP2PBdQT4Ko4YG8vc0JcvalsjWdpvm8GY,579
testapp/migrations/0023_number.py,sha256=PwDR1hX4rLQjRo-fLpM0LmJMAV52cAbRjcgkqxQ-rCY,703
testapp/migrations/0024_publisher_book.py,sha256=YkSquvTCv4ixNA143tvJAdM_Se7-XeAw8wnHRPkiMJk,1744
testapp/migrations/0025_modelwithnullablefieldsofdifferenttypes.py,sha256=t9bKB3CpmKcDq8UbUPkhP64MpKzaEdBmvrMwUB8Syrc,676
testapp/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
testapp/migrations/__pycache__/0001_initial.cpython-313.pyc,,
testapp/migrations/__pycache__/0002_test_unique_nullable_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0003_test_unique_nullable_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0004_test_unique_type_change_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0005_test_unique_type_change_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0006_test_remove_onetoone_field_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0007_test_remove_onetoone_field_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0008_test_drop_table_with_foreign_key_reference_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0009_test_drop_table_with_foreign_key_reference_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0010_pizza_topping.cpython-313.pyc,,
testapp/migrations/__pycache__/0011_test_unique_constraints.cpython-313.pyc,,
testapp/migrations/__pycache__/0012_test_indexes_retained_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0013_test_indexes_retained_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0014_test_rename_m2mfield_part1.cpython-313.pyc,,
testapp/migrations/__pycache__/0015_test_rename_m2mfield_part2.cpython-313.pyc,,
testapp/migrations/__pycache__/0016_jsonmodel.cpython-313.pyc,,
testapp/migrations/__pycache__/0017_binarydata_testcheckconstraintwithunicode_and_more.cpython-313.pyc,,
testapp/migrations/__pycache__/0018_choice_question.cpython-313.pyc,,
testapp/migrations/__pycache__/0019_customer_name_customer_address.cpython-313.pyc,,
testapp/migrations/__pycache__/0020_autofield_to_bigautofield.cpython-313.pyc,,
testapp/migrations/__pycache__/0021_multiple_autofield_to_bigauto.cpython-313.pyc,,
testapp/migrations/__pycache__/0022_timezone.cpython-313.pyc,,
testapp/migrations/__pycache__/0023_number.cpython-313.pyc,,
testapp/migrations/__pycache__/0024_publisher_book.cpython-313.pyc,,
testapp/migrations/__pycache__/0025_modelwithnullablefieldsofdifferenttypes.cpython-313.pyc,,
testapp/migrations/__pycache__/__init__.cpython-313.pyc,,
testapp/models.py,sha256=uhdoTj1zZBGLIIVOkyKpppMjrislH8sUHVNJlkRWpAc,8359
testapp/runners.py,sha256=hbWEf1hQ5jU-gG4rg0lp6502MZ8m-N1uV2FgWbUG8h8,1390
testapp/settings.py,sha256=krJBKmcbon4pgAC2RrBpzTBckREtGxnCUo2zouVJ5lU,21926
testapp/tests/__init__.py,sha256=fRFTloQCrFHE7_87TYMaH6ZFOLu-9z5ujeVjl2pZ5ys,461
testapp/tests/__pycache__/__init__.cpython-313.pyc,,
testapp/tests/__pycache__/test_bitshift.cpython-313.pyc,,
testapp/tests/__pycache__/test_constraints.cpython-313.pyc,,
testapp/tests/__pycache__/test_expressions.cpython-313.pyc,,
testapp/tests/__pycache__/test_fields.cpython-313.pyc,,
testapp/tests/__pycache__/test_getorcreate.cpython-313.pyc,,
testapp/tests/__pycache__/test_indexes.cpython-313.pyc,,
testapp/tests/__pycache__/test_jsonfield.cpython-313.pyc,,
testapp/tests/__pycache__/test_lookups.cpython-313.pyc,,
testapp/tests/__pycache__/test_multiple_databases.cpython-313.pyc,,
testapp/tests/__pycache__/test_queries.cpython-313.pyc,,
testapp/tests/__pycache__/test_timezones.cpython-313.pyc,,
testapp/tests/test_bitshift.py,sha256=Dj_V19EgBEVLY9OBlTwmdGEMTS4fZJR-S68E-NnMT3w,868
testapp/tests/test_constraints.py,sha256=HgXvnq7_5kZbIjaS47qjkfXHvhaOBbDJObjBdofIhm4,11269
testapp/tests/test_expressions.py,sha256=1llDf-jbAEMU0GpMHeCgg3O6ny6sQqLtvBWU4VbXlok,5820
testapp/tests/test_fields.py,sha256=0SGloPYhstqsKms0-gF8mTsaVL3OvBKrP-6073DygLY,1324
testapp/tests/test_getorcreate.py,sha256=9Hy-iV1hz-C_AkL0JVmPwf30peUst332Zvzl-jkP870,1739
testapp/tests/test_indexes.py,sha256=gHYY5U0HRpTDTWSAj1MUjgtJqiHZOSeJFr7Jm5CFxAc,15885
testapp/tests/test_jsonfield.py,sha256=54KF1oh_mqEIC7pbz604XS8Sj09UZkBEbC4Lx6iBW8Y,2008
testapp/tests/test_lookups.py,sha256=lP0h_x25GZf06EFidQJQzp_qTRi27VtTDO-cGjsy5Mw,484
testapp/tests/test_multiple_databases.py,sha256=OEz4SShOo26Mnbbbp4hWz3_X2ttPy6tsyTxZAXogB2I,4124
testapp/tests/test_queries.py,sha256=hiXKlGLJZwMU1HNcdQk4tBawsaTkM2uvnJTnUZKyizA,1586
testapp/tests/test_timezones.py,sha256=vqI9wkOV3cJ7fSOAAElY-xZkr5Kfg_qmXiYvu_FFbhM,4172
