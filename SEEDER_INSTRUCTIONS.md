# Database Seeder Instructions

## Overview
The database seeder script has been fixed to resolve all import errors and align with the actual Django models in the Hospital Management API.

## Fixed Issues
1. **Removed invalid 'NhanVien' model import** - This model doesn't exist in the codebase
2. **Fixed model field references** - Updated all field names to match actual model definitions
3. **Corrected appointment workflow** - Added proper work schedules creation before appointments
4. **Fixed telemedicine sessions** - Updated to use correct model name `PhienTuVanTuXa`
5. **Updated status values** - Changed to match actual model choices (e.g., "Hoan thanh" instead of "Đã hoàn thành")

## Available Seeder Options

### Option 1: Django Management Command (Recommended)
```bash
# Navigate to project directory
cd "g:\My Drive\Study\University\Python\DoAn\HospitalManagementAPI"

# Activate virtual environment
.venv\Scripts\activate

# Navigate to Django project
cd hospital_management

# Run the seeder command
python manage.py seed_db
```

### Option 2: Standalone Script
```bash
# Navigate to project directory
cd "g:\My Drive\Study\University\Python\DoAn\HospitalManagementAPI"

# Activate virtual environment
.venv\Scripts\activate

# Navigate to Django project
cd hospital_management

# Run the seeder script
python run_seeder.py
```

### Option 3: Batch File (Windows)
```bash
# From the project root directory
.\run_seeder.bat
```

## What the Seeder Creates

The seeder populates the database with realistic sample data:

- **Admin User**: username=`admin`, password=`admin123`
- **3 Patients** with complete profile information
- **2 Medical Facilities** (Bach Mai Hospital, Cho Ray Hospital)  
- **10 Medical Specialties** (5 per facility)
- **3 Doctors** with different specializations
- **6 Medical Services** (3 per facility)
- **Work Schedules** for doctors over the next 15 days
- **5 Sample Appointments** with various statuses
- **Telemedicine Sessions** for completed appointments
- **Payment Records** for confirmed/completed appointments

## Models Updated

### Core Models Fixed:
- `users.models.BenhNhan` (Patient)
- `medical.models.CoSoYTe` (Medical Facility)
- `medical.models.ChuyenKhoa` (Medical Specialty)
- `medical.models.BacSi` (Doctor)
- `medical.models.DichVu` (Medical Service)
- `appointments.models.LichLamViec` (Work Schedule)
- `appointments.models.LichHen` (Appointment)
- `appointments.models.PhienTuVanTuXa` (Telemedicine Session)
- `payments.models.ThanhToan` (Payment)

### Key Corrections Made:
1. **Field Name Alignment**: All field references match actual model definitions
2. **Relationship Handling**: Proper foreign key assignments
3. **Status Values**: Updated to use exact choices from model definitions
4. **Data Flow**: Correct order of data creation (users → patients → facilities → specialties → doctors → services → schedules → appointments → sessions → payments)

## Troubleshooting

If you encounter issues:

1. **Ensure virtual environment is activated**:
   ```bash
   .venv\Scripts\activate
   ```

2. **Check Django migrations are applied**:
   ```bash
   python manage.py migrate
   ```

3. **Verify Django settings**:
   ```bash
   python manage.py check
   ```

4. **Run directly in Django shell**:
   ```bash
   python manage.py shell
   >>> exec(open('seed_database.py').read())
   ```

## Success Verification

After successful seeding, you should see:
- Database populated with sample data
- Admin user created for testing
- API endpoints ready for testing with realistic data
- Complete workflow from patients to payments

The seeder automatically clears existing data before creating new sample data, making it safe to run multiple times during development.
