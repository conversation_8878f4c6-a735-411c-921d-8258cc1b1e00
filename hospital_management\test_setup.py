#!/usr/bin/env python
"""
Simple test script to check Django setup and model imports
"""
import os
import sys
import django

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hospital_management.settings')

try:
    django.setup()
    print("✓ Django setup successful")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    sys.exit(1)

try:
    from django.contrib.auth import get_user_model
    User = get_user_model()
    print(f"✓ User model imported: {User}")
except Exception as e:
    print(f"✗ User model import failed: {e}")

try:
    from users.models import BenhNhan
    print(f"✓ BenhNhan model imported: {BenhNhan}")
except Exception as e:
    print(f"✗ BenhNhan model import failed: {e}")

try:
    from medical.models import CoSoYTe, ChuyenKhoa, BacSi, DichVu
    print(f"✓ Medical models imported: {CoSoYTe}, {<PERSON>yenKhoa}, {BacSi}, {DichVu}")
except Exception as e:
    print(f"✗ Medical models import failed: {e}")

try:
    from appointments.models import LichHen, PhienTuVanTuXa, LichLamViec
    print(f"✓ Appointment models imported: {LichHen}, {PhienTuVanTuXa}, {LichLamViec}")
except Exception as e:
    print(f"✗ Appointment models import failed: {e}")

try:
    from payments.models import ThanhToan
    print(f"✓ Payment model imported: {ThanhToan}")
except Exception as e:
    print(f"✗ Payment model import failed: {e}")

# Test database connection
try:
    user_count = User.objects.count()
    print(f"✓ Database connection working - {user_count} users in database")
except Exception as e:
    print(f"✗ Database connection failed: {e}")

print("\nTest completed!")
