# Generated by Django 4.2 on 2023-05-03 15:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("testapp", "0023_number"),
    ]

    operations = [
        migrations.CreateModel(
            name="Publisher",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Book",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=100)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "authors",
                    models.ManyToManyField(related_name="books", to="testapp.author"),
                ),
                (
                    "publisher",
                    models.Foreign<PERSON>ey(
                        db_column="publisher_id_column",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="books",
                        to="testapp.publisher",
                    ),
                ),
            ],
        ),
    ]
