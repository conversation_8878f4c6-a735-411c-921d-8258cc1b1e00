from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from drf_spectacular.utils import extend_schema, extend_schema_view
from .models import BenhNhan
from .serializers import BenhNhanSerializer, BenhNhanCreateSerializer
from authentication.permissions import IsAdminUser, IsPatientUser, IsOwnerOrReadOnly


@extend_schema_view(
    list=extend_schema(
        operation_id='patients_list',
        tags=['Patients'],
        summary='List patients',
        description='Get list of patients with filtering, searching and ordering capabilities'
    ),
    create=extend_schema(
        operation_id='patients_create',
        tags=['Patients'],
        summary='Create patient',
        description='Create a new patient record'
    ),
    retrieve=extend_schema(
        operation_id='patients_retrieve',
        tags=['Patients'],
        summary='Get patient details',
        description='Get detailed information about a specific patient'
    ),
    update=extend_schema(
        operation_id='patients_update',
        tags=['Patients'],
        summary='Update patient',
        description='Update patient information'
    ),
    partial_update=extend_schema(
        operation_id='patients_partial_update',
        tags=['Patients'],
        summary='Partially update patient',
        description='Partially update patient information'
    ),
    destroy=extend_schema(
        operation_id='patients_delete',
        tags=['Patients'],
        summary='Delete patient',
        description='Delete a patient record'
    ),
)
class BenhNhanViewSet(viewsets.ModelViewSet):
    queryset = BenhNhan.objects.select_related('ma_nguoi_dung').all()
    serializer_class = BenhNhanSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['gioi_tinh', 'ma_nguoi_dung__trang_thai']
    search_fields = ['ho_ten', 'so_dien_thoai', 'email', 'cmnd_cccd', 'so_bhyt']
    ordering_fields = ['ho_ten', 'ngay_sinh', 'ma_nguoi_dung__ngay_tao']
    ordering = ['-ma_nguoi_dung__ngay_tao']
    
    def get_permissions(self):
        if self.action == 'create':
            permission_classes = [permissions.AllowAny]
        elif self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return BenhNhanCreateSerializer
        return BenhNhanSerializer
    
    def get_queryset(self):
        user = self.request.user
        if user.vai_tro == 'Benh nhan':
            # Bệnh nhân chỉ xem được thông tin của mình
            return self.queryset.filter(ma_nguoi_dung=user)
        elif user.vai_tro in ['Bac si', 'Nhan vien y te']:
            # Bác sĩ và nhân viên y tế xem được tất cả bệnh nhân
            return self.queryset
        elif user.vai_tro == 'Quan tri vien':
            # Admin xem được tất cả
            return self.queryset
        return self.queryset.none()
    
    @extend_schema(
        operation_id='patients_medical_history',
        tags=['Patients'],
        summary='Get patient medical history',
        description='Get medical history of a specific patient'
    )
    @action(detail=True, methods=['get'])
    def lich_su_kham(self, request, pk=None):
        """Lấy lịch sử khám bệnh của bệnh nhân"""
        benh_nhan = self.get_object()
        lich_hen = benh_nhan.lich_hen.select_related(
            'ma_bac_si', 'ma_dich_vu', 'ma_lich'
        ).order_by('-ngay_kham')
        
        # Import tại đây để tránh circular import
        from appointments.serializers import LichHenSerializer
        serializer = LichHenSerializer(lich_hen, many=True)
        return Response(serializer.data)
    
    @extend_schema(
        operation_id='patients_profile',
        tags=['Patients'],
        summary='Get current patient profile',
        description='Get profile information for the currently authenticated patient'
    )
    @action(detail=False, methods=['get'])
    def profile(self, request):
        """Lấy thông tin profile bệnh nhân hiện tại"""
        if request.user.vai_tro != 'Benh nhan':
            return Response(
                {'error': 'Chỉ bệnh nhân mới có thể truy cập endpoint này'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            benh_nhan = BenhNhan.objects.get(ma_nguoi_dung=request.user)
            serializer = self.get_serializer(benh_nhan)
            return Response(serializer.data)
        except BenhNhan.DoesNotExist:
            return Response(
                {'error': 'Không tìm thấy thông tin bệnh nhân'}, 
                status=status.HTTP_404_NOT_FOUND
            )
