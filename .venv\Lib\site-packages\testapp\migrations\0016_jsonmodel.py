# Generated by Django 4.0.1 on 2022-02-01 15:58

from django import VERSION
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testapp', '0015_test_rename_m2mfield_part2'),
    ]

    # <PERSON><PERSON><PERSON><PERSON> added in Django 3.1
    if VERSION >= (3, 1):
        operations = [
            migrations.CreateModel(
                name='JSONModel',
                fields=[
                    ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                    ('value', models.JSONField()),
                ],
                options={
                    'required_db_features': {'supports_json_field'},
                },
            ),
        ]
    else:
        pass
