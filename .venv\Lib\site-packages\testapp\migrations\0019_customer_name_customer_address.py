# Generated by Django 4.0.3 on 2022-03-24 14:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('testapp', '0018_choice_question'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer_name',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Customer_name', models.CharField(max_length=100)),
            ],
            options={
                'ordering': ['Customer_name'],
            },
        ),
        migrations.CreateModel(
            name='Customer_address',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Customer_address', models.CharField(max_length=100)),
                ('Customer_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='testapp.customer_name')),
            ],
            options={
                'ordering': ['Customer_address'],
            },
        ),
    ]
